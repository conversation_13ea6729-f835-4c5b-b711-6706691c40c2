<?php
/**
 * dmrthema WooCommerce hooks
 *
 * @package dmrthema
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Layout - Remove Default WooCommerce Actions
 * -----------------------------------------
 */
remove_action( 'woocommerce_before_main_content', 'woocommerce_breadcrumb', 20 );
remove_action( 'woocommerce_before_main_content', 'woocommerce_output_content_wrapper', 10 );
remove_action( 'woocommerce_after_main_content', 'woocommerce_output_content_wrapper_end', 10 );
remove_action( 'woocommerce_sidebar', 'woocommerce_get_sidebar', 10 );
remove_action( 'woocommerce_after_shop_loop', 'woocommerce_pagination', 10 );
remove_action( 'woocommerce_before_shop_loop', 'woocommerce_result_count', 20 );
remove_action( 'woocommerce_before_shop_loop', 'woocommerce_catalog_ordering', 30 );
// Notices'lari sorting wrapper'dan cikar
remove_action( 'woocommerce_before_shop_loop', 'woocommerce_output_all_notices', 10 );

/**
 * Global Layout Actions
 * -------------------
 */
add_action( 'woocommerce_before_main_content', 'dmrthema_before_content', 10 );
add_action( 'woocommerce_after_main_content', 'dmrthema_after_content', 10 );

/**
 * Product Loop Layout
 * ------------------
 */
// Sorting and Filtering
add_action( 'woocommerce_before_shop_loop', 'dmrthema_sorting_wrapper', 9 );
add_action( 'woocommerce_before_shop_loop', 'dmrthema_sorting_wrapper_close', 31 );
add_action( 'woocommerce_before_shop_loop', 'dmrthema_product_columns_wrapper', 40 );

// Pagination and Results
add_action( 'woocommerce_before_shop_loop', 'dmrthema_woocommerce_pagination', 30 );
add_action( 'woocommerce_after_shop_loop', 'woocommerce_pagination', 30 );

// End Wrappers
add_action( 'woocommerce_after_shop_loop', 'dmrthema_sorting_wrapper_end', 9 );
add_action( 'woocommerce_after_shop_loop', 'dmrthema_sorting_wrapper_close', 31 );
add_action( 'woocommerce_after_shop_loop', 'dmrthema_product_columns_wrapper_close', 40 );

/**
 * Sorting and Results Count Display
 * -------------------------------
 */
add_action( 'woocommerce_before_shop_loop', 'woocommerce_result_count', 10 );
add_action( 'woocommerce_after_shop_loop', 'woocommerce_result_count', 10 );

add_action( 'woocommerce_before_shop_loop', 'dmrthema_sorting_filters_wrapper_start', 15 );
add_action( 'woocommerce_before_shop_loop', 'woocommerce_catalog_ordering', 20 );
add_action( 'woocommerce_before_shop_loop', 'dmrthema_shop_filters_button', 25 );
add_action( 'woocommerce_before_shop_loop', 'dmrthema_sorting_filters_wrapper_end', 30 );

add_action( 'woocommerce_after_shop_loop', 'woocommerce_catalog_ordering', 20 );

/**
 * Single Product Layout
 * -------------------
 */
remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_meta', 40 );
add_action( 'woocommerce_after_single_product_summary', 'woocommerce_template_single_meta', 15 );

/**
 * Related Products Display
 * ----------------------
 */
remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );
add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );

/**
 * Product Badge and Rating Display
 * -----------------------------
 */
add_action( 'woocommerce_before_shop_loop_item_title', 'dmrthema_shop_out_of_stock', 8 );
remove_action( 'woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_rating', 5 );
add_action( 'woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_rating', 6 );

/**
 * Cart Fragment Support
 * -------------------
 */
if ( defined( 'WC_VERSION' ) && version_compare( WC_VERSION, '2.3', '>=' ) ) {
	add_filter( 'woocommerce_add_to_cart_fragments', 'dmrthema_cart_link_fragment' );
} else {
	add_filter( 'add_to_cart_fragments', 'dmrthema_cart_link_fragment' );
}

/**
 * Cart link fragment for AJAX cart updates
 */
if ( ! function_exists( 'dmrthema_cart_link_fragment' ) ) {
	function dmrthema_cart_link_fragment( $fragments ) {
		ob_start();
		dmrthema_cart_link();
		$fragments['a.cart-contents'] = ob_get_clean();
		return $fragments;
	}
}

/**
 * Cart Link
 * Displayed a link to the cart including the number of items present and the cart total
 */
if ( ! function_exists( 'dmrthema_cart_link' ) ) {
	function dmrthema_cart_link() {
		?>
		<a class="cart-contents" href="<?php echo esc_url( wc_get_cart_url() ); ?>" title="<?php esc_attr_e( 'View your shopping cart', 'dmrthema' ); ?>">
			<span class="amount"><?php echo wp_kses_data( WC()->cart->get_cart_subtotal() ); ?></span> 
			<span class="count"><?php echo wp_kses_data( sprintf( _n( '%d item', '%d items', WC()->cart->get_cart_contents_count(), 'dmrthema' ), WC()->cart->get_cart_contents_count() ) );?></span>
		</a>
		<?php
	}
}

/**
 * Display Header Cart
 */
if ( ! function_exists( 'dmrthema_header_cart' ) ) {
	function dmrthema_header_cart() {
		if ( is_cart() ) {
			$class = 'current-menu-item';
		} else {
			$class = '';
		}
		?>
		<ul id="site-header-cart" class="site-header-cart">
			<li class="<?php echo esc_attr( $class ); ?>">
				<?php dmrthema_cart_link(); ?>
			</li>
			<li>
				<?php
				$instance = array(
					'title' => '',
				);

				the_widget( 'WC_Widget_Cart', $instance );
				?>
			</li>
		</ul>
		<?php
	}
}

/**
 * Sorting wrapper
 */
if ( ! function_exists( 'dmrthema_sorting_wrapper' ) ) {
	function dmrthema_sorting_wrapper() {
		echo '<div class="dmrthema-sorting">';
	}
}

/**
 * Sorting wrapper close
 */
if ( ! function_exists( 'dmrthema_sorting_wrapper_close' ) ) {
	function dmrthema_sorting_wrapper_close() {
		echo '</div>';
	}
}

/**
 * Sorting wrapper end
 */
if ( ! function_exists( 'dmrthema_sorting_wrapper_end' ) ) {
	function dmrthema_sorting_wrapper_end() {
		echo '<div class="dmrthema-sorting">';
	}
}

/**
 * Product columns wrapper
 */
if ( ! function_exists( 'dmrthema_product_columns_wrapper' ) ) {
	function dmrthema_product_columns_wrapper() {
		$columns = wc_get_loop_prop( 'columns' );
		echo '<div class="columns-' . absint( $columns ) . '">';
	}
}

/**
 * Product columns wrapper close
 */
if ( ! function_exists( 'dmrthema_product_columns_wrapper_close' ) ) {
	function dmrthema_product_columns_wrapper_close() {
		echo '</div>';
	}
}

/**
 * Shop messages
 */
if ( ! function_exists( 'dmrthema_shop_messages' ) ) {
	function dmrthema_shop_messages() {
		if ( ! is_checkout() ) {
			if ( function_exists( 'wc_print_notices' ) ) {
				wc_print_notices();
			}
		}
	}
}

/**
 * WooCommerce pagination
 */
if ( ! function_exists( 'dmrthema_woocommerce_pagination' ) ) {
	function dmrthema_woocommerce_pagination() {
		if ( wc_get_loop_prop( 'is_paginated' ) ) {
			woocommerce_pagination();
		}
	}
}

/**
 * Breadcrumbs
 */
if ( ! function_exists( 'dmrthema_breadcrumbs' ) ) {
	function dmrthema_breadcrumbs() {
		if ( function_exists( 'woocommerce_breadcrumb' ) ) {
			woocommerce_breadcrumb();
		}
	}
}

/**
 * Sorting and Filters Wrapper Start
 */
if ( ! function_exists( 'dmrthema_sorting_filters_wrapper_start' ) ) {
	function dmrthema_sorting_filters_wrapper_start() {
		if ( is_shop() || is_product_category() || is_product_tag() ) {
			echo '<div class="sorting-filters-wrapper">';
		}
	}
}

/**
 * Sorting and Filters Wrapper End
 */
if ( ! function_exists( 'dmrthema_sorting_filters_wrapper_end' ) ) {
	function dmrthema_sorting_filters_wrapper_end() {
		if ( is_shop() || is_product_category() || is_product_tag() ) {
			echo '</div>';
		}
	}
}

/**
 * Shop Filters Button
 */
if ( ! function_exists( 'dmrthema_shop_filters_button' ) ) {
	function dmrthema_shop_filters_button() {
		if ( is_shop() || is_product_category() || is_product_tag() ) {
			echo '<button type="button" class="shop-filters-toggle" id="shop-filters-toggle">';
			echo '<i class="fas fa-filter"></i> Filtreler';
			echo '</button>';
		}
	}
}

/**
 * Shop Filters Modal
 */
if ( ! function_exists( 'dmrthema_shop_filters_modal' ) ) {
	function dmrthema_shop_filters_modal() {
		if ( is_shop() || is_product_category() || is_product_tag() ) {
			?>
			<div id="shop-filters-modal" class="shop-filters-modal">
				<div class="shop-filters-modal-content">
					<div class="shop-filters-modal-header">
						<h3>Filtreler</h3>
						<button type="button" class="shop-filters-close" id="shop-filters-close">
							<i class="fas fa-times"></i>
						</button>
					</div>
					<div class="shop-filters-modal-body">
						<form id="shop-filters-form" method="get">
							<?php
							// Mevcut query parametrelerini koru
							if ( ! empty( $_GET ) ) {
								foreach ( $_GET as $key => $value ) {
									if ( ! in_array( $key, array( 'min_price', 'max_price', 'product_cat', 'product_tag', 'orderby' ) ) ) {
										echo '<input type="hidden" name="' . esc_attr( $key ) . '" value="' . esc_attr( $value ) . '">';
									}
								}
							}
							?>

							<!-- Fiyat Filtresi -->
							<div class="filter-group">
								<h4>Fiyat Araligi</h4>
								<div class="price-filter">
									<input type="number" name="min_price" placeholder="Min fiyat" value="<?php echo esc_attr( $_GET['min_price'] ?? '' ); ?>">
									<span>-</span>
									<input type="number" name="max_price" placeholder="Max fiyat" value="<?php echo esc_attr( $_GET['max_price'] ?? '' ); ?>">
								</div>
							</div>

							<!-- Kategori Filtresi -->
							<?php
							$product_categories = get_terms( array(
								'taxonomy' => 'product_cat',
								'hide_empty' => true,
							) );

							if ( ! empty( $product_categories ) && ! is_wp_error( $product_categories ) ) :
								// URL'den gelen kategori parametrelerini isle
								$selected_categories = array();
								if ( isset( $_GET['product_cat'] ) ) {
									if ( is_array( $_GET['product_cat'] ) ) {
										$selected_categories = $_GET['product_cat'];
									} else {
										// Virgülle ayrılmış string'i array'e çevir
										$selected_categories = explode( ',', $_GET['product_cat'] );
									}
								}
							?>
							<div class="filter-group">
								<h4>Kategoriler</h4>
								<div class="category-filter">
									<?php foreach ( $product_categories as $category ) : ?>
									<label class="filter-checkbox">
										<input type="checkbox" name="product_cat[]" value="<?php echo esc_attr( $category->slug ); ?>"
											<?php checked( in_array( $category->slug, $selected_categories ) ); ?>>
										<span><?php echo esc_html( $category->name ); ?> (<?php echo $category->count; ?>)</span>
									</label>
									<?php endforeach; ?>
								</div>
							</div>
							<?php endif; ?>

							<!-- Etiket Filtresi -->
							<?php
							$product_tags = get_terms( array(
								'taxonomy' => 'product_tag',
								'hide_empty' => true,
							) );

							if ( ! empty( $product_tags ) && ! is_wp_error( $product_tags ) ) :
								// URL'den gelen etiket parametrelerini isle
								$selected_tags = array();
								if ( isset( $_GET['product_tag'] ) ) {
									if ( is_array( $_GET['product_tag'] ) ) {
										$selected_tags = $_GET['product_tag'];
									} else {
										// Virgülle ayrılmış string'i array'e çevir
										$selected_tags = explode( ',', $_GET['product_tag'] );
									}
								}
							?>
							<div class="filter-group">
								<h4>Etiketler</h4>
								<div class="tag-filter">
									<?php foreach ( $product_tags as $tag ) : ?>
									<label class="filter-checkbox">
										<input type="checkbox" name="product_tag[]" value="<?php echo esc_attr( $tag->slug ); ?>"
											<?php checked( in_array( $tag->slug, $selected_tags ) ); ?>>
										<span><?php echo esc_html( $tag->name ); ?> (<?php echo $tag->count; ?>)</span>
									</label>
									<?php endforeach; ?>
								</div>
							</div>
							<?php endif; ?>

							<!-- Stok Durumu Filtresi -->
							<?php
							// URL'den gelen stok durumu parametrelerini isle
							$selected_stock_status = array();
							if ( isset( $_GET['stock_status'] ) ) {
								if ( is_array( $_GET['stock_status'] ) ) {
									$selected_stock_status = $_GET['stock_status'];
								} else {
									// Virgülle ayrılmış string'i array'e çevir
									$selected_stock_status = explode( ',', $_GET['stock_status'] );
								}
							}
							?>
							<div class="filter-group">
								<h4>Stok Durumu</h4>
								<div class="stock-filter">
									<label class="filter-checkbox">
										<input type="checkbox" name="stock_status[]" value="instock"
											<?php checked( in_array( 'instock', $selected_stock_status ) ); ?>>
										<span>Stokta Var</span>
									</label>
									<label class="filter-checkbox">
										<input type="checkbox" name="stock_status[]" value="outofstock"
											<?php checked( in_array( 'outofstock', $selected_stock_status ) ); ?>>
										<span>Stokta Yok</span>
									</label>
								</div>
							</div>
						</form>
					</div>
					<div class="shop-filters-modal-footer">
						<button type="button" class="btn-clear-filters" id="clear-filters">Temizle</button>
						<button type="submit" form="shop-filters-form" class="btn-apply-filters">Uygula</button>
					</div>
				</div>
			</div>
			<?php
		}
	}
}

// Modal'i footer'da goster
add_action( 'wp_footer', 'dmrthema_shop_filters_modal' );
